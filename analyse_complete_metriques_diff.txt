ANALYSE COMPLÈTE DES MÉTRIQUES DIFF - ENTROPIE BACCARAT ANALYZER
================================================================

Date: 2025-07-10
Source: entropie_baccarat_analyzer.jl
Objectif: Comprendre précisément comment sont calculées toutes les métriques Diff

================================================================
ARCHITECTURE GÉNÉRALE DES CALCULS
================================================================

Le système calcule 7 métriques Diff basées sur les variations entre mains consécutives:

1. DiffEG = |H_AEP_théo(n) - H_AEP_théo(n-1)| → Variation information théorique
2. DiffSEG = |StructEG(n) - StructEG(n-1)| → Variation ratio théorie/réalité  
3. DiffCEG = |ConfEG(n) - ConfEG(n-1)| → Variation distance théorie/réalité
4. DiffT5 = |T5(n) - T5(n-1)| → Variation taux entropique
5. DiffDivEG = |H_Shannon(n) - H_Shannon(n-1)| → Variation diversité observée
6. DiffC = |H_cond(n) - H_cond(n-1)| → Variation prédictibilité contextuelle
7. Mt5 = T5 / log₂(18) → Entropie métrique normalisée (stabilité informationnelle)

================================================================
CALCULS DE BASE FONDAMENTAUX
================================================================

FONCTION UTILITAIRE CRITIQUE:
-----------------------------
safe_log(x, base, epsilon):
- Si x ≤ 0 → x = epsilon (1e-12)
- Retourne: log(x) / log(base)
- Évite les erreurs log(0) = -∞

PROBABILITÉS THÉORIQUES INDEX5:
-------------------------------
Stockées dans analyzer.theoretical_probs
18 valeurs INDEX5 possibles avec probabilités uniformes théoriques

================================================================
MÉTRIQUES DE BASE (8 MÉTRIQUES FONDAMENTALES)
================================================================

1. DIVENTROPG (H_Shannon) - DIVERSITÉ OBSERVÉE
----------------------------------------------
Fonction: calculate_shannon_entropy(analyzer, probabilities)
Formule: H(X) = -∑ p_obs(x) log₂ p_obs(x)

Calcul étape par étape:
1. Compter occurrences de chaque INDEX5 dans la séquence
2. Calculer probabilités empiriques: p_obs(x) = count(x) / longueur_totale
3. Appliquer formule Shannon: entropy = -∑ p * safe_log(p, base, epsilon)

2. ENTROPG (H_AEP_théo) - INFORMATION THÉORIQUE
-----------------------------------------------
Fonction: calculate_sequence_entropy_aep(analyzer, sequence)
Formule: H_AEP = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))

Calcul étape par étape:
1. Pour chaque INDEX5 dans la séquence
2. Récupérer p_théo depuis analyzer.theoretical_probs
3. Calculer: total_log_prob += safe_log(p_théo, base, epsilon)
4. Retourner: -total_log_prob / longueur_séquence

3. CONDITIONNELLE (H_cond) - PRÉDICTIBILITÉ CONTEXTUELLE
--------------------------------------------------------
Fonction: calculate_conditional_entropy(analyzer, sequence)
Formule: H(X|Y) = ∑ P(y) × H(X|Y=y)

Calcul étape par étape:
1. Analyser transitions: contexte[i] → symbole[i+1]
2. Pour chaque contexte unique:
   - Compter transitions vers chaque symbole suivant
   - Calculer probabilité du contexte: P(contexte) = count_contexte / total_transitions
   - Reconstruire séquence conditionnelle pour ce contexte
   - Calculer H(X|contexte) via calculate_sequence_entropy_aep
3. Sommer: conditional_entropy += P(contexte) × H(X|contexte)

4. T5 (Taux d'Entropie) - COMPLEXITÉ TEMPORELLE
-----------------------------------------------
Fonction: calculate_entropy_rate_new(analyzer, sequence)
Formule: T5 = H_AEP(fenêtre_5_derniers_éléments)

Calcul étape par étape:
1. Extraire fenêtre de 5 derniers éléments: sequence[(end-4):end]
2. Calculer H₅ = calculate_sequence_entropy_aep(analyzer, window_5)
3. Retourner H₅ directement (pas de division)

5. Mt5 (Entropie Métrique Kolmogorov-Sinai) - STABILITÉ INFORMATIONNELLE
------------------------------------------------------------------------
Fonction: calculate_metric_from_t5(analyzer, t5_value)
Formule: Mt5 = T5 / log₂(18)

Calcul étape par étape:
1. Calculer T5 via calculate_entropy_rate_new
2. Normaliser: Mt5 = T5 / log₂(18) = T5 / 4.1699
3. Représente la complexité informationnelle normalisée

6. EGobs (Entropie Générale Observée) - INFORMATION RÉELLE
----------------------------------------------------------
Fonction: calculate_sequence_entropy_aep_observed(analyzer, sequence)
Formule: H_AEP_obs = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_obs(xᵢ))

Calcul étape par étape:
1. Compter occurrences: counts[value] = nombre d'apparitions
2. Pour chaque INDEX5 dans la séquence:
   - Calculer p_obs = counts[value] / longueur_totale
   - Ajouter: total_log_prob += safe_log(p_obs, base, epsilon)
3. Retourner: -total_log_prob / longueur_séquence

7. ConfEG (Conformité Entropique) - DISTANCE THÉORIE/RÉALITÉ
------------------------------------------------------------
Fonction: calculate_conf_eg(entrop_g, eg_obs)
Formule: ConfEG = |EntropG - EGobs| = |H_AEP_théo - H_AEP_obs|

Calcul étape par étape:
1. Calculer EntropG via calculate_sequence_entropy_aep
2. Calculer EGobs via calculate_sequence_entropy_aep_observed
3. Retourner: abs(entrop_g - eg_obs)

8. StructEG (Structure Entropique) - RATIO THÉORIE/RÉALITÉ
----------------------------------------------------------
Fonction: calculate_struct_eg(entrop_g, eg_obs)
Formule: StructEG = EntropG / EGobs

Calcul étape par étape:
1. Calculer EntropG et EGobs (comme ci-dessus)
2. Si eg_obs ≈ 0 → retourner 1.0 (éviter division par 0)
3. Sinon retourner: entrop_g / eg_obs

================================================================
CALCULS DES DIFFÉRENTIELS (7 MÉTRIQUES DIFF)
================================================================

Toutes les métriques Diff suivent le même pattern:
DiffX(n) = |X(n) - X(n-1)|

1. DiffEG = calculate_diff_entrop_g(current_theoretical, previous_theoretical)
   = |simple_entropy_theoretical(n) - simple_entropy_theoretical(n-1)|

2. DiffSEG = calculate_diff_seg(current_struct_eg, previous_struct_eg)
   = |StructEG(n) - StructEG(n-1)|

3. DiffCEG = calculate_diff_ceg(current_conf_eg, previous_conf_eg)
   = |ConfEG(n) - ConfEG(n-1)|

4. DiffT5 = calculate_diff_taux(current_rate, previous_rate)
   = |T5(n) - T5(n-1)|

5. DiffDivEG = calculate_diff_div_entrop_g(current_simple, previous_simple)
   = |simple_entropy(n) - simple_entropy(n-1)|

6. DiffC = calculate_diff_cond(current_conditional, previous_conditional)
   = |conditional_entropy(n) - conditional_entropy(n-1)|

7. Mt5 = T5 / log₂(18) (pas un différentiel, mais une normalisation)

================================================================
PROCESSUS COMPLET DE CALCUL
================================================================

Pour chaque main n dans la séquence:

ÉTAPE 1: CALCUL DES MÉTRIQUES DE BASE
1. Extraire sous-séquence[1:n]
2. Calculer 8 métriques de base pour cette sous-séquence
3. Stocker dans EntropyMetrics{T}

ÉTAPE 2: CALCUL DES DIFFÉRENTIELS
1. Si n = 1 → tous les différentiels = 0 (pas de main précédente)
2. Si n > 1 → calculer différentiels entre main n et main n-1
3. Stocker dans PredictiveDifferentials{T}

ÉTAPE 3: CALCUL DU SCORE COMPOSITE
Formule entropique optimale:
SCORE = Mt5 × exp(-DiffEG) × exp(-DiffDivEG) × exp(-DiffC) / ((1 + DiffSEG + DiffCEG) × (1 + DiffT5))

================================================================
SIGNIFICATION PHYSIQUE DES MÉTRIQUES
================================================================

Mt5: Complexité informationnelle normalisée du système INDEX5
DiffEG: Instabilité de l'information théorique entre mains
DiffSEG: Instabilité du rapport théorie/réalité
DiffCEG: Instabilité de la distance théorie/réalité
DiffT5: Instabilité temporelle du taux entropique
DiffDivEG: Instabilité de la diversité observée
DiffC: Instabilité de la prédictibilité contextuelle

Ces métriques capturent la structure entropique profonde du système INDEX5
et permettent d'identifier les points d'équilibre entropique où la
prédictibilité est maximale selon la théorie de l'information de Shannon.

================================================================
SYSTÈME PRÉDICTIF - CALCULS POUR CHAQUE INDEX5 POSSIBLE
================================================================

FONCTION: calculate_predictive_differentials(analyzer, sequence, position)

Pour prédire la main n+1 à partir de la position n:

ÉTAPE 1: ANALYSE DE LA MAIN COURANTE
1. Extraire current_sequence = sequence[1:position]
2. Calculer evolution = calculate_all_metrics_evolution(analyzer, current_sequence, 4)
3. Récupérer current_metrics = evolution[end]

ÉTAPE 2: DÉTERMINATION DES INDEX5 POSSIBLES
1. current_index5 = sequence[position]
2. required_index1 = calculate_required_index1(current_index5)
3. valid_index5_values = get_valid_index5_values(required_index1)
   → 6 valeurs possibles selon les règles INDEX1

ÉTAPE 3: SIMULATION POUR CHAQUE INDEX5 POSSIBLE
Pour chaque possible_index5 dans valid_index5_values:

3.1. CRÉATION SÉQUENCE SIMULÉE
simulated_sequence = vcat(current_sequence, [possible_index5])

3.2. CALCUL MÉTRIQUES SIMULÉES
- simulated_conditional = calculate_conditional_entropy(analyzer, simulated_sequence)
- simulated_simple = calculate_shannon_entropy(analyzer, empirical_probs_simulées)
- simulated_theoretical = calculate_sequence_entropy_aep(analyzer, simulated_sequence)
- simulated_rate = calculate_entropy_rate_new(analyzer, simulated_sequence)

3.3. CALCUL MÉTRIQUES COMPLÈTES SIMULÉES
simulated_evolution = calculate_all_metrics_evolution(analyzer, simulated_sequence, 4)
simulated_metrics = simulated_evolution[end]

3.4. CALCUL DES DIFFÉRENTIELS PRÉDICTIFS
- diff_cond = |simulated_conditional - current_metrics.conditional_entropy|
- diff_taux = |simulated_rate - current_metrics.entropy_rate|
- diff_div_entrop_g = |simulated_simple - current_metrics.simple_entropy|
- diff_entrop_g = |simulated_theoretical - current_metrics.simple_entropy_theoretical|
- diff_seg = |simulated_metrics.struct_eg - current_metrics.struct_eg|
- diff_ceg = |simulated_metrics.conf_eg - current_metrics.conf_eg|

3.5. CALCUL DU SCORE PRÉDICTIF
score = calculate_predictive_score(
    simulated_metrics.metric_entropy,   # Mt5
    diff_entrop_g,                      # DiffEG
    diff_div_entrop_g,                  # DiffDivEG
    diff_cond,                          # DiffC
    diff_seg,                           # DiffSEG
    diff_ceg,                           # DiffCEG
    diff_taux                           # DiffT5
)

ÉTAPE 4: SÉLECTION DE LA MEILLEURE PRÉDICTION
1. Comparer les 6 scores calculés
2. Sélectionner l'INDEX5 avec le score maximal
3. Retourner cette prédiction

================================================================
FORMULES MATHÉMATIQUES EXACTES EMPLOYÉES
================================================================

1. ENTROPIE DE SHANNON (Base de tout le système)
H(X) = -∑ p(x) log₂ p(x)

2. ENTROPIE AEP THÉORIQUE
H_AEP_théo = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))

3. ENTROPIE AEP OBSERVÉE
H_AEP_obs = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_obs(xᵢ))

4. ENTROPIE CONDITIONNELLE
H(X|Y) = ∑ P(y) × H(X|Y=y) = -∑∑ P(x,y) × log₂ P(x|y)

5. TAUX D'ENTROPIE T5
T5 = H_AEP(fenêtre_5_derniers_éléments)

6. ENTROPIE MÉTRIQUE Mt5
Mt5 = T5 / log₂(18) = T5 / 4.1699

7. CONFORMITÉ ENTROPIQUE
ConfEG = |H_AEP_théo - H_AEP_obs|

8. STRUCTURE ENTROPIQUE
StructEG = H_AEP_théo / H_AEP_obs

9. DIFFÉRENTIELS GÉNÉRIQUES
DiffX(n) = |X(n) - X(n-1)|

10. SCORE PRÉDICTIF ENTROPIQUE OPTIMAL
SCORE = Mt5 × exp(-DiffEG) × exp(-DiffDivEG) × exp(-DiffC) / ((1 + DiffSEG + DiffCEG) × (1 + DiffT5))

================================================================
GESTION DES CAS LIMITES
================================================================

1. LOGARITHME DE ZÉRO
safe_log(x, base, epsilon):
- Si x ≤ 0 → x = epsilon = 1e-12
- Évite log(0) = -∞

2. DIVISION PAR ZÉRO
StructEG = EntropG / EGobs:
- Si EGobs ≈ 0 → retourner 1.0

3. SÉQUENCES COURTES
- Si longueur < 2 → entropie conditionnelle = 0
- Si longueur < 5 → T5 = 0

4. SCORE INFINI
calculate_predictive_score:
- Si dénominateur ≈ 0 → retourner +∞

================================================================
CONCLUSION - CHAÎNE COMPLÈTE DE CALCUL
================================================================

NIVEAU 1: CALCULS ATOMIQUES
- safe_log() pour éviter les singularités
- Comptage des occurrences et calcul des probabilités

NIVEAU 2: ENTROPIES DE BASE
- Shannon, AEP théorique, AEP observée, conditionnelle

NIVEAU 3: MÉTRIQUES DÉRIVÉES
- T5, Mt5, ConfEG, StructEG

NIVEAU 4: DIFFÉRENTIELS
- 7 métriques Diff = variations entre mains consécutives

NIVEAU 5: SCORE COMPOSITE
- Formule entropique optimale combinant toutes les métriques

NIVEAU 6: PRÉDICTION
- Simulation des 6 INDEX5 possibles
- Sélection du score maximal

Cette architecture en 6 niveaux garantit la cohérence mathématique
et la robustesse numérique du système prédictif entropique.
