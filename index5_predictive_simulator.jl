#!/usr/bin/env julia
# ═══════════════════════════════════════════════════════════════════════════════
# INDEX5 PREDICTIVE SIMULATOR
# ═══════════════════════════════════════════════════════════════════════════════
"""
INDEX5 PREDICTIVE SIMULATOR - Simulateur de Prédiction INDEX5
=============================================================

OBJECTIF:
    Programme de simulation prédictive pour le baccarat basé sur INDEX5.
    Calcule les 7 métriques d'entropie (DiffEG, DiffSEG, DiffCEG, Mt5, DiffT5, DiffDivEG, DiffC)
    pour les 9 valeurs INDEX5 possibles à chaque main n+1.

FONCTIONNEMENT:
    - Analyse automatique du fichier le plus récent du dossier partie
    - Simulation des mains 5 à 59 (pour prédire mains 6 à 60)
    - Application des règles INDEX1 déterministes
    - Calcul exhaustif des métriques d'entropie

ARCHITECTURE:
    - Moteur de calcul: entropie_baccarat_analyzer.jl
    - Structures de données spécialisées
    - Pipeline de simulation automatisé
    - Export multi-format (TXT, CSV)

AUTEUR: Système d'analyse entropique
VERSION: 1.0
DATE: 2025-01-10
"""

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 1: IMPORTS ET DÉPENDANCES
# ═══════════════════════════════════════════════════════════════════════════════

# Import du module principal d'analyse d'entropie
include("entropie_baccarat_analyzer.jl")

# Imports système
using Printf
using Statistics
using Dates

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 2: STRUCTURES DE DONNÉES SPÉCIALISÉES
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section définit les structures de données utilisées pour stocker
# les résultats de simulation et organiser les calculs de métriques.
#
# STRUCTURES PRINCIPALES:
# - PredictiveSimulationResult: Résultat pour une valeur INDEX5
# - HandSimulationResults: Résultats pour une main complète (9 simulations)
# ═══════════════════════════════════════════════════════════════════════════════

"""
    PredictiveSimulationResult{T<:AbstractFloat}

Structure pour stocker les résultats de simulation pour une valeur INDEX5.
Contient toutes les métriques de base et différentiels calculés.
"""
struct PredictiveSimulationResult{T<:AbstractFloat}
    index5_value::String                    # Valeur INDEX5 simulée
    
    # Métriques de base (8 métriques)
    mt5::T                                  # Mt5 (Entropie Métrique)
    conditionnelle::T                       # Entropie Conditionnelle
    t5::T                                   # T5 (Taux d'Entropie)
    div_entrop_g::T                         # DivEntropG (Diversité Entropique)
    entrop_g::T                             # EntropG (Entropie Générale)
    eg_obs::T                               # EGobs (Entropie Générale Observée)
    conf_eg::T                              # ConfEG (Conformité Entropique)
    struct_eg::T                            # StructEG (Structure Entropique)
    
    # Différentiels (7 différentiels)
    diff_eg::T                              # DiffEG
    diff_seg::T                             # DiffSEG
    diff_ceg::T                             # DiffCEG
    diff_t5::T                              # DiffT5
    diff_div_eg::T                          # DiffDivEG
    diff_c::T                               # DiffC
    diff_egobs::T                           # DiffEGobs (bonus)
    
    # Score composite
    score::T                                # SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
end

"""
    HandSimulationResults{T<:AbstractFloat}

Structure pour stocker tous les résultats de simulation pour une main n.
Contient les 9 simulations possibles pour la main n+1.
"""
struct HandSimulationResults{T<:AbstractFloat}
    hand_position::Int                      # Position de la main n
    current_index5::String                  # INDEX5 observé à la main n
    required_index1::Int                    # INDEX1 obligatoire pour main n+1
    
    # Résultats pour les 9 valeurs possibles
    simulations::Dict{String, PredictiveSimulationResult{T}}
    
    # Statistiques de simulation
    best_index5::String                     # Meilleure prédiction (score max)
    best_score::T                           # Score maximal
    worst_index5::String                    # Pire prédiction (score min)
    worst_score::T                          # Score minimal
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 3: RÈGLES INDEX1 DÉTERMINISTES
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section implémente les règles déterministes pour INDEX1 selon INDEX2:
#
# RÈGLES FONDAMENTALES:
# - Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
# - Si INDEX2 = A : INDEX1 se conserve (0→0, 1→1)
# - Si INDEX2 = B : INDEX1 se conserve (0→0, 1→1)
#
# FONCTIONS PRINCIPALES:
# - apply_index1_rules(): Application des règles
# - generate_possible_index5_values(): Génération des 9 valeurs possibles
# ═══════════════════════════════════════════════════════════════════════════════

"""
    apply_index1_rules(current_index5::String) -> Int

Applique les règles déterministes INDEX1 pour déterminer INDEX1 à la main n+1.

RÈGLES EXACTES IMPLÉMENTÉES :
- Si INDEX1 = 0 à la main n avec INDEX2 = C à la main n, alors à la main n+1 : INDEX1 sera égal à 1
- Si INDEX1 = 1 à la main n avec INDEX2 = C à la main n, alors à la main n+1 : INDEX1 sera égal à 0
- Si INDEX1 = 0 à la main n avec INDEX2 = A à la main n, alors à la main n+1 : INDEX1 sera égal à 0
- Si INDEX1 = 1 à la main n avec INDEX2 = A à la main n, alors à la main n+1 : INDEX1 sera égal à 1
- Si INDEX1 = 0 à la main n avec INDEX2 = B à la main n, alors à la main n+1 : INDEX1 sera égal à 0
- Si INDEX1 = 1 à la main n avec INDEX2 = B à la main n, alors à la main n+1 : INDEX1 sera égal à 1

RÉSUMÉ :
- Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
- Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
"""
function apply_index1_rules(current_index5::String)
    if isempty(current_index5)
        return nothing
    end

    try
        parts = split(current_index5, '_')
        if length(parts) < 3
            return nothing
        end

        current_index1 = parse(Int, parts[1])
        current_index2 = parts[2]

        # APPLICATION EXACTE DES RÈGLES
        if current_index2 == "C"
            # RÈGLE C : INDEX1 s'inverse
            if current_index1 == 0
                return 1  # 0_C_* → INDEX1 = 1 pour main n+1
            else  # current_index1 == 1
                return 0  # 1_C_* → INDEX1 = 0 pour main n+1
            end
        elseif current_index2 == "A"
            # RÈGLE A : INDEX1 se conserve
            if current_index1 == 0
                return 0  # 0_A_* → INDEX1 = 0 pour main n+1
            else  # current_index1 == 1
                return 1  # 1_A_* → INDEX1 = 1 pour main n+1
            end
        elseif current_index2 == "B"
            # RÈGLE B : INDEX1 se conserve
            if current_index1 == 0
                return 0  # 0_B_* → INDEX1 = 0 pour main n+1
            else  # current_index1 == 1
                return 1  # 1_B_* → INDEX1 = 1 pour main n+1
            end
        else
            return nothing  # INDEX2 invalide
        end
    catch
        return nothing
    end
end

"""
    generate_possible_index5_values(required_index1::Int) -> Vector{String}

Génère les 9 valeurs INDEX5 possibles pour INDEX1 donné.
Format : INDEX1_INDEX2_INDEX3 avec INDEX2 ∈ {A,B,C} et INDEX3 ∈ {BANKER,PLAYER,TIE}
"""
function generate_possible_index5_values(required_index1::Int)
    possible_values = String[]
    
    for index2 in ["A", "B", "C"]
        for index3 in ["BANKER", "PLAYER", "TIE"]
            push!(possible_values, "$(required_index1)_$(index2)_$(index3)")
        end
    end
    
    return possible_values
end

"""
    test_index1_rules()

Fonction de test pour vérifier que les règles INDEX1 sont correctement implémentées.
Teste tous les cas possibles et affiche les résultats.
"""
function test_index1_rules()
    println("\n🧪 TEST DES RÈGLES INDEX1 DÉTERMINISTES")
    println("="^60)

    # Test cases avec les règles exactes
    test_cases = [
        # RÈGLES INDEX2 = C (inversion)
        ("0_C_BANKER", 1, "0_C_BANKER → INDEX1 = 1 (inversion)"),
        ("0_C_PLAYER", 1, "0_C_PLAYER → INDEX1 = 1 (inversion)"),
        ("0_C_TIE", 1, "0_C_TIE → INDEX1 = 1 (inversion)"),
        ("1_C_BANKER", 0, "1_C_BANKER → INDEX1 = 0 (inversion)"),
        ("1_C_PLAYER", 0, "1_C_PLAYER → INDEX1 = 0 (inversion)"),
        ("1_C_TIE", 0, "1_C_TIE → INDEX1 = 0 (inversion)"),

        # RÈGLES INDEX2 = A (conservation)
        ("0_A_BANKER", 0, "0_A_BANKER → INDEX1 = 0 (conservation)"),
        ("0_A_PLAYER", 0, "0_A_PLAYER → INDEX1 = 0 (conservation)"),
        ("0_A_TIE", 0, "0_A_TIE → INDEX1 = 0 (conservation)"),
        ("1_A_BANKER", 1, "1_A_BANKER → INDEX1 = 1 (conservation)"),
        ("1_A_PLAYER", 1, "1_A_PLAYER → INDEX1 = 1 (conservation)"),
        ("1_A_TIE", 1, "1_A_TIE → INDEX1 = 1 (conservation)"),

        # RÈGLES INDEX2 = B (conservation)
        ("0_B_BANKER", 0, "0_B_BANKER → INDEX1 = 0 (conservation)"),
        ("0_B_PLAYER", 0, "0_B_PLAYER → INDEX1 = 0 (conservation)"),
        ("0_B_TIE", 0, "0_B_TIE → INDEX1 = 0 (conservation)"),
        ("1_B_BANKER", 1, "1_B_BANKER → INDEX1 = 1 (conservation)"),
        ("1_B_PLAYER", 1, "1_B_PLAYER → INDEX1 = 1 (conservation)"),
        ("1_B_TIE", 1, "1_B_TIE → INDEX1 = 1 (conservation)")
    ]

    all_passed = true

    for (input_index5, expected_index1, description) in test_cases
        result = apply_index1_rules(input_index5)

        if result == expected_index1
            println("✅ $description")
        else
            println("❌ $description - ERREUR: obtenu $result, attendu $expected_index1")
            all_passed = false
        end
    end

    println("\n" * "="^60)
    if all_passed
        println("🎉 TOUS LES TESTS PASSÉS - Règles INDEX1 correctement implémentées!")
    else
        println("⚠️  CERTAINS TESTS ONT ÉCHOUÉ - Vérifier l'implémentation")
    end

    # Test de génération des 9 valeurs possibles
    println("\n🔢 TEST GÉNÉRATION DES 9 VALEURS POSSIBLES")
    println("-"^60)

    for index1 in [0, 1]
        possible_values = generate_possible_index5_values(index1)
        println("INDEX1 = $index1 → $(length(possible_values)) valeurs possibles:")
        for (i, value) in enumerate(possible_values)
            println("  $i. $value")
        end
        println()
    end

    return all_passed
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 5: MOTEUR DE SIMULATION PRÉDICTIVE
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section contient le cœur algorithmique du simulateur.
#
# FONCTIONNALITÉS PRINCIPALES:
# - Simulation exhaustive des 9 valeurs INDEX5 possibles
# - Calcul des métriques d'entropie pour chaque simulation
# - Calcul des différentiels entre main n et main n+1
# - Détermination de la meilleure prédiction par score composite
#
# ALGORITHME:
# 1. Déterminer INDEX1 obligatoire selon les règles
# 2. Générer les 9 valeurs INDEX5 possibles
# 3. Pour chaque valeur: simuler et calculer les métriques
# 4. Calculer les différentiels et le score composite
# 5. Identifier la meilleure et la pire prédiction
#
# FONCTION PRINCIPALE:
# - simulate_index5_prediction(): Simulation pour une main
# ═══════════════════════════════════════════════════════════════════════════════

"""
    simulate_index5_prediction(
        analyzer::EntropyAnalyzer{T},
        sequence::Vector{String},
        hand_position::Int
    ) where T -> HandSimulationResults{T}

OBJECTIF:
    À la main n (hand_position), simule les 9 valeurs INDEX5 possibles pour la main n+1.
    Calcule toutes les métriques d'entropie pour chacune des 9 simulations.

PROCESSUS:
    1. Analyser INDEX5 à la main n
    2. Déterminer INDEX1 obligatoire pour main n+1 (règles déterministes)
    3. Générer les 9 valeurs INDEX5 possibles (3 INDEX2 × 3 INDEX3)
    4. Pour chaque valeur simulée:
       - Créer séquence simulée [mains 1 à n] + [main n+1 simulée]
       - Calculer toutes les métriques d'entropie
       - Calculer les différentiels par rapport à la main n
    5. Identifier la meilleure prédiction (score composite maximal)

MÉTRIQUES CALCULÉES:
    - 8 métriques de base: Mt5, Conditionnelle, T5, DivEntropG, EntropG, EGobs, ConfEG, StructEG
    - 7 différentiels: DiffEG, DiffSEG, DiffCEG, DiffT5, DiffDivEG, DiffC, DiffEGobs
    - 1 score composite pour classement
"""
function simulate_index5_prediction(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    hand_position::Int
) where T<:AbstractFloat

    if hand_position < 1 || hand_position > length(sequence)
        error("Position de main invalide: $hand_position")
    end

    # ÉTAPE 1: Analyser la main n actuelle
    current_index5 = sequence[hand_position]
    println("   🎯 Main $hand_position: INDEX5 = $current_index5")

    # ÉTAPE 2: Déterminer INDEX1 obligatoire pour main n+1 selon les règles
    required_index1 = apply_index1_rules(current_index5)
    if isnothing(required_index1)
        error("Impossible de déterminer INDEX1 pour: $current_index5")
    end
    println("   📋 INDEX1 obligatoire pour main $(hand_position + 1): $required_index1")

    # ÉTAPE 3: Générer les 9 valeurs INDEX5 possibles
    possible_values = generate_possible_index5_values(required_index1)
    println("   🔢 9 valeurs INDEX5 possibles générées")

    # ÉTAPE 4: Calculer les métriques de référence (main n)
    current_sequence = sequence[1:hand_position]
    current_evolution = calculate_all_metrics_evolution(analyzer, current_sequence, 4)

    if isempty(current_evolution)
        error("Impossible de calculer l'évolution pour la séquence actuelle")
    end

    current_metrics = current_evolution[end]
    println("   ✅ Métriques de référence calculées pour main $hand_position")

    # ÉTAPE 5: Dictionnaire pour stocker les résultats des 9 simulations
    simulations = Dict{String, PredictiveSimulationResult{T}}()

    # ÉTAPE 6: Simuler chaque valeur INDEX5 possible (9 simulations)
    println("   🔄 Simulation des 9 valeurs INDEX5 possibles...")
    for (i, possible_index5) in enumerate(possible_values)
        # Créer la séquence simulée: [mains 1 à n] + [main n+1 simulée]
        simulated_sequence = vcat(current_sequence, [possible_index5])

        # Calculer toutes les métriques pour la séquence simulée
        simulated_evolution = calculate_all_metrics_evolution(analyzer, simulated_sequence, 4)

        if isempty(simulated_evolution)
            @warn "Échec simulation pour $possible_index5"
            continue
        end

        # Métriques de la main n+1 simulée
        simulated_metrics = simulated_evolution[end]
        
        # ÉTAPE 7: Calculer les différentiels entre main n et main n+1 simulée
        # Ces différentiels mesurent l'impact de chaque valeur INDEX5 simulée
        diff_eg = calculate_diff_entrop_g(simulated_metrics.simple_entropy_theoretical, current_metrics.simple_entropy_theoretical)
        diff_seg = calculate_diff_seg(simulated_metrics.struct_eg, current_metrics.struct_eg)
        diff_ceg = calculate_diff_ceg(simulated_metrics.conf_eg, current_metrics.conf_eg)
        diff_t5 = calculate_diff_taux(simulated_metrics.entropy_rate, current_metrics.entropy_rate)
        diff_div_eg = calculate_diff_div_entrop_g(simulated_metrics.simple_entropy, current_metrics.simple_entropy)
        diff_c = calculate_diff_cond(simulated_metrics.conditional_entropy, current_metrics.conditional_entropy)
        diff_egobs = calculate_diff_egobs(simulated_metrics.entropy_aep_observed, current_metrics.entropy_aep_observed)

        # ÉTAPE 8: Calculer le score composite pour classement
        score = calculate_predictive_score(diff_c, diff_t5, diff_div_eg, diff_eg)

        # ÉTAPE 9: Créer le résultat de simulation complet
        simulation_result = PredictiveSimulationResult{T}(
            possible_index5,                            # Valeur INDEX5 simulée
            simulated_metrics.metric_entropy,           # Mt5 (Entropie Métrique)
            simulated_metrics.conditional_entropy,      # Conditionnelle
            simulated_metrics.entropy_rate,             # T5 (Taux d'Entropie)
            simulated_metrics.simple_entropy,           # DivEntropG (Diversité Entropique)
            simulated_metrics.simple_entropy_theoretical, # EntropG (Entropie Générale)
            simulated_metrics.entropy_aep_observed,     # EGobs (Entropie Générale Observée)
            simulated_metrics.conf_eg,                  # ConfEG (Conformité Entropique)
            simulated_metrics.struct_eg,                # StructEG (Structure Entropique)
            diff_eg,                                    # DiffEG
            diff_seg,                                   # DiffSEG
            diff_ceg,                                   # DiffCEG
            diff_t5,                                    # DiffT5
            diff_div_eg,                                # DiffDivEG
            diff_c,                                     # DiffC
            diff_egobs,                                 # DiffEGobs (bonus)
            score                                       # Score composite
        )

        # Stocker le résultat de cette simulation
        simulations[possible_index5] = simulation_result

        # Affichage de progression pour chaque simulation
        if i % 3 == 0 || i == length(possible_values)
            println("     ✓ Simulation $i/$(length(possible_values)): $possible_index5 (Score: $(round(score, digits=4)))")
        end
    end

    println("   ✅ 9 simulations terminées pour main $hand_position")
    
    # ÉTAPE 10: Analyser les résultats et identifier la meilleure prédiction
    best_index5 = ""
    best_score = T(-Inf)
    worst_index5 = ""
    worst_score = T(Inf)

    for (index5, result) in simulations
        if result.score > best_score
            best_score = result.score
            best_index5 = index5
        end
        if result.score < worst_score
            worst_score = result.score
            worst_index5 = index5
        end
    end

    println("   🏆 Meilleure prédiction: $best_index5 (Score: $(round(best_score, digits=6)))")
    println("   📉 Pire prédiction: $worst_index5 (Score: $(round(worst_score, digits=6)))")

    # ÉTAPE 11: Retourner les résultats complets de la simulation
    return HandSimulationResults{T}(
        hand_position,                  # Position de la main n
        current_index5,                 # INDEX5 observé à la main n
        required_index1,                # INDEX1 obligatoire pour main n+1
        simulations,                    # Dict des 9 simulations
        best_index5,                    # Meilleure prédiction
        best_score,                     # Score maximal
        worst_index5,                   # Pire prédiction
        worst_score                     # Score minimal
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 4: GESTION DES FICHIERS ET CHARGEMENT AUTOMATIQUE
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section gère le chargement automatique des données depuis le dossier partie.
#
# FONCTIONNALITÉS:
# - Détection automatique du fichier le plus récent
# - Validation de l'existence du dossier partie
# - Tri par date de modification
# - Gestion d'erreurs robuste
#
# FONCTION PRINCIPALE:
# - find_latest_file_in_partie(): Trouve le fichier le plus récent
# ═══════════════════════════════════════════════════════════════════════════════

"""
    find_latest_file_in_partie() -> String

Trouve automatiquement le fichier le plus récent dans le dossier partie.
"""
function find_latest_file_in_partie()
    partie_dir = "partie"

    if !isdir(partie_dir)
        error("Dossier 'partie' introuvable!")
    end

    files = readdir(partie_dir)
    json_files = filter(f -> endswith(f, ".json"), files)

    if isempty(json_files)
        error("Aucun fichier JSON trouvé dans le dossier partie!")
    end

    # Trier par date de modification (plus récent en premier)
    full_paths = [joinpath(partie_dir, f) for f in json_files]
    sorted_files = sort(full_paths, by = f -> stat(f).mtime, rev = true)

    latest_file = sorted_files[1]
    println("📁 Fichier le plus récent détecté: $(basename(latest_file))")

    return latest_file
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 6: SIMULATION SPÉCIALISÉE MAINS 5-59
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section implémente la simulation spécialisée pour les mains 5 à 59.
#
# SPÉCIFICATIONS:
# - Simulation des mains 5 à 59 incluses (55 mains au total)
# - Prédiction pour les mains 6 à 60
# - Validation de la longueur minimale de séquence (60 mains)
# - Affichage de progression en temps réel
# - Gestion d'erreurs par main individuelle
#
# MÉTRIQUES CALCULÉES:
# - DiffEG, DiffSEG, DiffCEG, Mt5, DiffT5, DiffDivEG, DiffC
#
# FONCTION PRINCIPALE:
# - simulate_hands_5_to_59(): Simulation spécialisée
# ═══════════════════════════════════════════════════════════════════════════════

"""
    simulate_hands_5_to_59(
        analyzer::EntropyAnalyzer{T},
        sequence::Vector{String}
    ) where T -> Vector{HandSimulationResults{T}}

Simule les 9 valeurs INDEX5 possibles pour les mains 5 à 59 (prédiction mains 6 à 60).
Calcule les 7 métriques demandées : DiffEG, DiffSEG, DiffCEG, Mt5, DiffT5, DiffDivEG, DiffC.
"""
function simulate_hands_5_to_59(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat

    if length(sequence) < 60
        error("Séquence trop courte! Minimum 60 mains requises, trouvé: $(length(sequence))")
    end

    results = HandSimulationResults{T}[]

    println("🔄 SIMULATION SPÉCIALISÉE MAINS 5 À 59")
    println("="^60)
    println("📋 Objectif: Simuler les 9 valeurs INDEX5 possibles pour chaque main n")
    println("📊 Métriques: DiffEG, DiffSEG, DiffCEG, Mt5, DiffT5, DiffDivEG, DiffC")
    println("🎯 Prédiction: Mains 6 à 60 (55 prédictions au total)")
    println()

    # BOUCLE PRINCIPALE: Simuler de la main 5 à la main 59 (incluses)
    for hand_position in 5:59
        println("\n" * "─"^60)
        println("🎲 SIMULATION MAIN $hand_position → PRÉDICTION MAIN $(hand_position + 1)")
        println("─"^60)

        try
            # Lancer la simulation pour cette main
            simulation_result = simulate_index5_prediction(analyzer, sequence, hand_position)
            push!(results, simulation_result)

            # Affichage de progression globale
            progress = hand_position - 4
            total = 59 - 5 + 1
            percentage = round(progress / total * 100, digits=1)
            println("   📈 Progression globale: $progress/$total ($percentage%)")

        catch e
            @warn "❌ ERREUR simulation main $hand_position: $e"
            continue
        end
    end

    println("✅ Simulation terminée! $(length(results)) mains simulées avec succès")
    return results
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 7: AFFICHAGE ET GÉNÉRATION DE RAPPORTS
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section gère l'affichage des résultats et la génération de rapports.
#
# FONCTIONNALITÉS D'AFFICHAGE:
# - Résumé statistique des simulations
# - Distribution des meilleures prédictions
# - Métriques globales (moyenne, médiane, min/max)
#
# FORMATS D'EXPORT:
# - Rapport détaillé TXT: Tableau complet par main
# - Export CSV: Données structurées pour analyse externe
# - Métadonnées: Date, nombre de simulations, statistiques
#
# FONCTIONS PRINCIPALES:
# - display_simulation_summary(): Affichage résumé
# - generate_detailed_simulation_report(): Rapport TXT détaillé
# - export_simulation_csv(): Export CSV structuré
# ═══════════════════════════════════════════════════════════════════════════════

"""
    display_simulation_summary(results::Vector{HandSimulationResults{T}}) where T

Affiche un résumé des résultats de simulation.
"""
function display_simulation_summary(results::Vector{HandSimulationResults{T}}) where T<:AbstractFloat
    if isempty(results)
        println("❌ Aucun résultat de simulation à afficher")
        return
    end

    println("\n" * "="^80)
    println("📊 RÉSUMÉ DE SIMULATION PRÉDICTIVE INDEX5")
    println("="^80)
    println("📏 Nombre de mains simulées: $(length(results))")

    # Statistiques globales
    all_scores = Float64[]
    best_predictions = String[]

    for result in results
        for (index5, sim) in result.simulations
            push!(all_scores, sim.score)
        end
        push!(best_predictions, result.best_index5)
    end

    if !isempty(all_scores)
        println("📈 Score moyen: $(round(mean(all_scores), digits=6))")
        println("📊 Score médian: $(round(median(all_scores), digits=6))")
        println("📉 Score min/max: $(round(minimum(all_scores), digits=6)) / $(round(maximum(all_scores), digits=6))")
    end

    # Distribution des meilleures prédictions
    prediction_counts = Dict{String, Int}()
    for pred in best_predictions
        prediction_counts[pred] = get(prediction_counts, pred, 0) + 1
    end

    println("\n🏆 DISTRIBUTION DES MEILLEURES PRÉDICTIONS:")
    sorted_predictions = sort(collect(prediction_counts), by = x -> x[2], rev = true)
    for (index5, count) in sorted_predictions[1:min(5, length(sorted_predictions))]
        percentage = round(count / length(best_predictions) * 100, digits=2)
        println("   $index5: $count fois ($percentage%)")
    end

    println("="^80)
end

"""
    generate_detailed_simulation_report(
        results::Vector{HandSimulationResults{T}},
        filename::String = "simulation_report.txt"
    ) where T

Génère un rapport détaillé de simulation au format texte.
"""
function generate_detailed_simulation_report(
    results::Vector{HandSimulationResults{T}},
    filename::String = "simulation_report.txt"
) where T<:AbstractFloat

    if isempty(results)
        @warn "Aucun résultat à exporter"
        return
    end

    try
        open(filename, "w") do file
            # En-tête du rapport
            println(file, "RAPPORT DÉTAILLÉ DE SIMULATION PRÉDICTIVE INDEX5")
            println(file, "="^60)
            println(file, "Date: $(Dates.now())")
            println(file, "Nombre de mains simulées: $(length(results))")
            println(file, "")

            # Tableau détaillé pour chaque main
            println(file, "DÉTAIL PAR MAIN:")
            println(file, "-"^60)

            for (i, result) in enumerate(results)
                println(file, "")
                println(file, "MAIN $(result.hand_position) - INDEX5 observé: $(result.current_index5)")
                println(file, "INDEX1 obligatoire pour main $(result.hand_position + 1): $(result.required_index1)")
                println(file, "Meilleure prédiction: $(result.best_index5) (Score: $(round(result.best_score, digits=6)))")
                println(file, "")

                # Tableau des 9 simulations
                println(file, @sprintf("%-15s %10s %10s %10s %10s %10s %10s %10s %12s",
                        "INDEX5", "DiffEG", "DiffSEG", "DiffCEG", "Mt5", "DiffT5", "DiffDivEG", "DiffC", "SCORE"))
                println(file, "-"^115)

                # Trier par score décroissant
                sorted_sims = sort(collect(result.simulations), by = x -> x[2].score, rev = true)

                for (index5, sim) in sorted_sims
                    println(file, @sprintf("%-15s %10.4f %10.4f %10.4f %10.4f %10.4f %10.4f %10.4f %12.6f",
                            index5,
                            sim.diff_eg,
                            sim.diff_seg,
                            sim.diff_ceg,
                            sim.mt5,
                            sim.diff_t5,
                            sim.diff_div_eg,
                            sim.diff_c,
                            sim.score))
                end

                if i < length(results)
                    println(file, "")
                    println(file, "-"^60)
                end
            end
        end

        println("📄 Rapport détaillé généré: $filename")

    catch e
        @error "Erreur lors de la génération du rapport: $e"
    end
end

"""
    export_simulation_csv(
        results::Vector{HandSimulationResults{T}},
        filename::String = "simulation_data.csv"
    ) where T

Exporte les résultats de simulation au format CSV pour analyse externe.
"""
function export_simulation_csv(
    results::Vector{HandSimulationResults{T}},
    filename::String = "simulation_data.csv"
) where T<:AbstractFloat

    if isempty(results)
        @warn "Aucun résultat à exporter"
        return
    end

    try
        open(filename, "w") do file
            # En-tête CSV
            println(file, "Hand_Position,Current_INDEX5,Required_INDEX1,Simulated_INDEX5,Mt5,Conditionnelle,T5,DivEntropG,EntropG,EGobs,ConfEG,StructEG,DiffEG,DiffSEG,DiffCEG,DiffT5,DiffDivEG,DiffC,DiffEGobs,Score,Is_Best")

            # Données pour chaque simulation
            for result in results
                for (index5, sim) in result.simulations
                    is_best = (index5 == result.best_index5) ? "TRUE" : "FALSE"

                    println(file, "$(result.hand_position),$(result.current_index5),$(result.required_index1),$index5,$(sim.mt5),$(sim.conditionnelle),$(sim.t5),$(sim.div_entrop_g),$(sim.entrop_g),$(sim.eg_obs),$(sim.conf_eg),$(sim.struct_eg),$(sim.diff_eg),$(sim.diff_seg),$(sim.diff_ceg),$(sim.diff_t5),$(sim.diff_div_eg),$(sim.diff_c),$(sim.diff_egobs),$(sim.score),$is_best")
                end
            end
        end

        println("📊 Données CSV exportées: $filename")

    catch e
        @error "Erreur lors de l'export CSV: $e"
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 8: PROGRAMME PRINCIPAL AUTOMATISÉ
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section contient le programme principal qui orchestre toute la simulation.
#
# PIPELINE D'EXÉCUTION:
# 1. Initialisation de l'analyseur d'entropie
# 2. Détection automatique du fichier le plus récent
# 3. Chargement et validation des données
# 4. Extraction de la séquence INDEX5
# 5. Validation de la longueur (minimum 60 mains)
# 6. Lancement de la simulation spécialisée (mains 5-59)
# 7. Affichage des résultats
# 8. Génération automatique des rapports (TXT + CSV)
#
# GESTION D'ERREURS:
# - Try-catch global avec messages explicites
# - Validation à chaque étape critique
# - Messages de progression en temps réel
#
# FONCTION PRINCIPALE:
# - main_simulation(): Point d'entrée automatisé
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main_simulation()

Programme principal automatisé pour la simulation prédictive INDEX5.
Charge automatiquement le fichier le plus récent et simule les mains 5-59.
"""
function main_simulation()
    println("🎰 SIMULATEUR PRÉDICTIF INDEX5 - BACCARAT")
    println("="^60)
    println("📋 Simulation automatisée des mains 5 à 59")
    println("📊 Calcul des 7 métriques pour 9 valeurs INDEX5 par main")
    println("🔧 Basé sur entropie_baccarat_analyzer.jl")
    println()

    try
        # Initialisation de l'analyseur
        println("⚙️  Initialisation de l'analyseur d'entropie...")
        analyzer = EntropyAnalyzer{Float64}()

        # Chargement automatique du fichier le plus récent
        println("🔍 Recherche du fichier le plus récent...")
        latest_file = find_latest_file_in_partie()

        # Chargement des données
        println("📁 Chargement des données...")
        parties = load_baccarat_data(latest_file)

        if isempty(parties)
            error("Aucune partie trouvée dans le fichier!")
        end

        println("✅ $(length(parties)) parties chargées")

        # Utiliser la première partie (ou demander à l'utilisateur)
        game_data = parties[1]
        sequence = extract_index5_sequence(game_data)

        if length(sequence) < 60
            error("Séquence trop courte! $(length(sequence)) mains trouvées, minimum 60 requises")
        end

        println("📏 Séquence: $(length(sequence)) mains")
        println("🎯 Simulation des mains 5 à 59 (prédiction mains 6 à 60)")
        println()

        # Lancer la simulation spécialisée
        results = simulate_hands_5_to_59(analyzer, sequence)

        # Afficher les résultats
        display_simulation_summary(results)

        # Génération automatique des rapports
        println("\n📄 Génération des rapports...")

        # Rapport détaillé
        report_filename = "simulation_mains_5_59_detailed.txt"
        generate_detailed_simulation_report(results, report_filename)

        # Export CSV
        csv_filename = "simulation_mains_5_59_data.csv"
        export_simulation_csv(results, csv_filename)

        println("\n✅ Simulation terminée avec succès!")
        println("📊 Consultez les fichiers générés pour les résultats détaillés")

    catch e
        println("❌ ERREUR: $e")
        println("🔧 Vérifiez que le dossier 'partie' contient des fichiers JSON valides")
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 9: FONCTIONS SUPPRIMÉES - PROGRAMME AUTOMATISÉ UNIQUEMENT
# ═══════════════════════════════════════════════════════════════════════════════
#
# Les fonctions interactives ont été supprimées pour se concentrer sur
# l'automatisation complète du processus de simulation.
#
# FONCTIONS SUPPRIMÉES:
# - simulate_single_game_interactive()
# - simulate_multiple_games_interactive()
# - simulate_custom_sequence_interactive()
#
# RAISON: Simplification de l'interface pour un usage automatisé
# ═══════════════════════════════════════════════════════════════════════════════

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 10: POINT D'ENTRÉE ET EXÉCUTION AUTOMATIQUE
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section finale gère l'exécution automatique du programme.
#
# COMPORTEMENT:
# - Exécution automatique si le fichier est lancé directement
# - Pas d'exécution si le fichier est inclus dans un autre programme
# - Permet l'utilisation en tant que module ou programme autonome
#
# USAGE:
# - Lancement direct: julia index5_predictive_simulator.jl
# - Inclusion: include("index5_predictive_simulator.jl")
# ═══════════════════════════════════════════════════════════════════════════════

# Exécution automatique si fichier lancé directement
if abspath(PROGRAM_FILE) == @__FILE__
    main_simulation()
end

# ═══════════════════════════════════════════════════════════════════════════════
# FIN DU PROGRAMME - INDEX5 PREDICTIVE SIMULATOR
# ═══════════════════════════════════════════════════════════════════════════════
