"""
INDEX5 PREDICTIVE SIMULATOR - Simulateur de Prédiction INDEX5
=============================================================

Programme de simulation prédictive pour le baccarat basé sur INDEX5.
Calcule les 7 métriques d'entropie (DiffEG, DiffSEG, DiffCEG, Mt5, DiffT5, DiffDivEG, DiffC)
pour les 9 valeurs INDEX5 possibles à chaque main n+1.

Analyse automatique du fichier le plus récent du dossier partie.
Simulation des mains 5 à 59 (pour prédire mains 6 à 60).

Utilise entropie_baccarat_analyzer.jl comme moteur de calcul.
"""

# Import du module principal d'analyse d'entropie
include("entropie_baccarat_analyzer.jl")

using Printf
using Statistics
using Dates

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURES DE DONNÉES SPÉCIALISÉES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    PredictiveSimulationResult{T<:AbstractFloat}

Structure pour stocker les résultats de simulation pour une valeur INDEX5.
Contient toutes les métriques de base et différentiels calculés.
"""
struct PredictiveSimulationResult{T<:AbstractFloat}
    index5_value::String                    # Valeur INDEX5 simulée
    
    # Métriques de base (8 métriques)
    mt5::T                                  # Mt5 (Entropie Métrique)
    conditionnelle::T                       # Entropie Conditionnelle
    t5::T                                   # T5 (Taux d'Entropie)
    div_entrop_g::T                         # DivEntropG (Diversité Entropique)
    entrop_g::T                             # EntropG (Entropie Générale)
    eg_obs::T                               # EGobs (Entropie Générale Observée)
    conf_eg::T                              # ConfEG (Conformité Entropique)
    struct_eg::T                            # StructEG (Structure Entropique)
    
    # Différentiels (7 différentiels)
    diff_eg::T                              # DiffEG
    diff_seg::T                             # DiffSEG
    diff_ceg::T                             # DiffCEG
    diff_t5::T                              # DiffT5
    diff_div_eg::T                          # DiffDivEG
    diff_c::T                               # DiffC
    diff_egobs::T                           # DiffEGobs (bonus)
    
    # Score composite
    score::T                                # SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
end

"""
    HandSimulationResults{T<:AbstractFloat}

Structure pour stocker tous les résultats de simulation pour une main n.
Contient les 9 simulations possibles pour la main n+1.
"""
struct HandSimulationResults{T<:AbstractFloat}
    hand_position::Int                      # Position de la main n
    current_index5::String                  # INDEX5 observé à la main n
    required_index1::Int                    # INDEX1 obligatoire pour main n+1
    
    # Résultats pour les 9 valeurs possibles
    simulations::Dict{String, PredictiveSimulationResult{T}}
    
    # Statistiques de simulation
    best_index5::String                     # Meilleure prédiction (score max)
    best_score::T                           # Score maximal
    worst_index5::String                    # Pire prédiction (score min)
    worst_score::T                          # Score minimal
end

# ═══════════════════════════════════════════════════════════════════════════════
# RÈGLES INDEX1 DÉTERMINISTES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    apply_index1_rules(current_index5::String) -> Int

Applique les règles déterministes INDEX1 pour déterminer INDEX1 à la main n+1.

Règles :
- Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
- Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
"""
function apply_index1_rules(current_index5::String)
    if isempty(current_index5)
        return nothing
    end
    
    try
        parts = split(current_index5, '_')
        if length(parts) < 2
            return nothing
        end
        
        current_index1 = parse(Int, parts[1])
        current_index2 = parts[2]
        
        if current_index2 == "C"
            return 1 - current_index1  # Inversion obligatoire
        else  # A ou B
            return current_index1      # Conservation obligatoire
        end
    catch
        return nothing
    end
end

"""
    generate_possible_index5_values(required_index1::Int) -> Vector{String}

Génère les 9 valeurs INDEX5 possibles pour INDEX1 donné.
Format : INDEX1_INDEX2_INDEX3 avec INDEX2 ∈ {A,B,C} et INDEX3 ∈ {BANKER,PLAYER,TIE}
"""
function generate_possible_index5_values(required_index1::Int)
    possible_values = String[]
    
    for index2 in ["A", "B", "C"]
        for index3 in ["BANKER", "PLAYER", "TIE"]
            push!(possible_values, "$(required_index1)_$(index2)_$(index3)")
        end
    end
    
    return possible_values
end

# ═══════════════════════════════════════════════════════════════════════════════
# MOTEUR DE SIMULATION PRÉDICTIVE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    simulate_index5_prediction(
        analyzer::EntropyAnalyzer{T},
        sequence::Vector{String},
        hand_position::Int
    ) where T -> HandSimulationResults{T}

Simule toutes les valeurs INDEX5 possibles pour la main n+1 et calcule
toutes les métriques d'entropie et différentiels pour chaque simulation.
"""
function simulate_index5_prediction(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    hand_position::Int
) where T<:AbstractFloat
    
    if hand_position < 1 || hand_position > length(sequence)
        error("Position de main invalide: $hand_position")
    end
    
    # INDEX5 actuel à la main n
    current_index5 = sequence[hand_position]
    
    # Déterminer INDEX1 obligatoire pour main n+1
    required_index1 = apply_index1_rules(current_index5)
    if isnothing(required_index1)
        error("Impossible de déterminer INDEX1 pour: $current_index5")
    end
    
    # Générer les 9 valeurs possibles
    possible_values = generate_possible_index5_values(required_index1)
    
    # Calculer les métriques actuelles (main n)
    current_sequence = sequence[1:hand_position]
    current_evolution = calculate_all_metrics_evolution(analyzer, current_sequence, 4)
    
    if isempty(current_evolution)
        error("Impossible de calculer l'évolution pour la séquence actuelle")
    end
    
    current_metrics = current_evolution[end]
    
    # Dictionnaire pour stocker les résultats de simulation
    simulations = Dict{String, PredictiveSimulationResult{T}}()
    
    # Simuler chaque valeur INDEX5 possible
    for possible_index5 in possible_values
        # Créer la séquence simulée (main n + main n+1 simulée)
        simulated_sequence = vcat(current_sequence, [possible_index5])
        
        # Calculer les métriques pour la séquence simulée
        simulated_evolution = calculate_all_metrics_evolution(analyzer, simulated_sequence, 4)
        
        if isempty(simulated_evolution)
            continue  # Ignorer cette simulation si échec
        end
        
        simulated_metrics = simulated_evolution[end]
        
        # Calculer les différentiels entre main n et main n+1 simulée
        diff_eg = calculate_diff_entrop_g(simulated_metrics.simple_entropy_theoretical, current_metrics.simple_entropy_theoretical)
        diff_seg = calculate_diff_seg(simulated_metrics.struct_eg, current_metrics.struct_eg)
        diff_ceg = calculate_diff_ceg(simulated_metrics.conf_eg, current_metrics.conf_eg)
        diff_t5 = calculate_diff_taux(simulated_metrics.entropy_rate, current_metrics.entropy_rate)
        diff_div_eg = calculate_diff_div_entrop_g(simulated_metrics.simple_entropy, current_metrics.simple_entropy)
        diff_c = calculate_diff_cond(simulated_metrics.conditional_entropy, current_metrics.conditional_entropy)
        diff_egobs = calculate_diff_egobs(simulated_metrics.entropy_aep_observed, current_metrics.entropy_aep_observed)
        
        # Calculer le score composite
        score = calculate_predictive_score(diff_c, diff_t5, diff_div_eg, diff_eg)
        
        # Créer le résultat de simulation
        simulation_result = PredictiveSimulationResult{T}(
            possible_index5,
            simulated_metrics.metric_entropy,           # mt5
            simulated_metrics.conditional_entropy,      # conditionnelle
            simulated_metrics.entropy_rate,             # t5
            simulated_metrics.simple_entropy,           # div_entrop_g
            simulated_metrics.simple_entropy_theoretical, # entrop_g
            simulated_metrics.entropy_aep_observed,     # eg_obs
            simulated_metrics.conf_eg,                  # conf_eg
            simulated_metrics.struct_eg,                # struct_eg
            diff_eg,                                    # diff_eg
            diff_seg,                                   # diff_seg
            diff_ceg,                                   # diff_ceg
            diff_t5,                                    # diff_t5
            diff_div_eg,                                # diff_div_eg
            diff_c,                                     # diff_c
            diff_egobs,                                 # diff_egobs
            score                                       # score
        )
        
        simulations[possible_index5] = simulation_result
    end
    
    # Trouver la meilleure et la pire prédiction
    best_index5 = ""
    best_score = T(-Inf)
    worst_index5 = ""
    worst_score = T(Inf)
    
    for (index5, result) in simulations
        if result.score > best_score
            best_score = result.score
            best_index5 = index5
        end
        if result.score < worst_score
            worst_score = result.score
            worst_index5 = index5
        end
    end
    
    return HandSimulationResults{T}(
        hand_position,
        current_index5,
        required_index1,
        simulations,
        best_index5,
        best_score,
        worst_index5,
        worst_score
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# CHARGEMENT AUTOMATIQUE DU FICHIER LE PLUS RÉCENT
# ═══════════════════════════════════════════════════════════════════════════════

"""
    find_latest_file_in_partie() -> String

Trouve automatiquement le fichier le plus récent dans le dossier partie.
"""
function find_latest_file_in_partie()
    partie_dir = "partie"

    if !isdir(partie_dir)
        error("Dossier 'partie' introuvable!")
    end

    files = readdir(partie_dir)
    json_files = filter(f -> endswith(f, ".json"), files)

    if isempty(json_files)
        error("Aucun fichier JSON trouvé dans le dossier partie!")
    end

    # Trier par date de modification (plus récent en premier)
    full_paths = [joinpath(partie_dir, f) for f in json_files]
    sorted_files = sort(full_paths, by = f -> stat(f).mtime, rev = true)

    latest_file = sorted_files[1]
    println("📁 Fichier le plus récent détecté: $(basename(latest_file))")

    return latest_file
end

# ═══════════════════════════════════════════════════════════════════════════════
# SIMULATION SPÉCIALISÉE MAINS 5-59
# ═══════════════════════════════════════════════════════════════════════════════

"""
    simulate_hands_5_to_59(
        analyzer::EntropyAnalyzer{T},
        sequence::Vector{String}
    ) where T -> Vector{HandSimulationResults{T}}

Simule les 9 valeurs INDEX5 possibles pour les mains 5 à 59 (prédiction mains 6 à 60).
Calcule les 7 métriques demandées : DiffEG, DiffSEG, DiffCEG, Mt5, DiffT5, DiffDivEG, DiffC.
"""
function simulate_hands_5_to_59(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat

    if length(sequence) < 60
        error("Séquence trop courte! Minimum 60 mains requises, trouvé: $(length(sequence))")
    end

    results = HandSimulationResults{T}[]

    println("🔄 Simulation des mains 5 à 59 (prédiction mains 6 à 60)...")
    println("📊 Calcul des 7 métriques pour 9 valeurs INDEX5 par main")

    # Simuler de la main 5 à la main 59 (incluses)
    for hand_position in 5:59
        try
            simulation_result = simulate_index5_prediction(analyzer, sequence, hand_position)
            push!(results, simulation_result)

            # Affichage de progression
            if (hand_position - 4) % 10 == 0
                progress = hand_position - 4
                total = 59 - 5 + 1
                println("   ✅ Main $hand_position simulée ($progress/$total)")
            end
        catch e
            @warn "❌ Erreur simulation main $hand_position: $e"
            continue
        end
    end

    println("✅ Simulation terminée! $(length(results)) mains simulées avec succès")
    return results
end

# ═══════════════════════════════════════════════════════════════════════════════
# AFFICHAGE ET RAPPORTS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    display_simulation_summary(results::Vector{HandSimulationResults{T}}) where T

Affiche un résumé des résultats de simulation.
"""
function display_simulation_summary(results::Vector{HandSimulationResults{T}}) where T<:AbstractFloat
    if isempty(results)
        println("❌ Aucun résultat de simulation à afficher")
        return
    end

    println("\n" * "="^80)
    println("📊 RÉSUMÉ DE SIMULATION PRÉDICTIVE INDEX5")
    println("="^80)
    println("📏 Nombre de mains simulées: $(length(results))")

    # Statistiques globales
    all_scores = Float64[]
    best_predictions = String[]

    for result in results
        for (index5, sim) in result.simulations
            push!(all_scores, sim.score)
        end
        push!(best_predictions, result.best_index5)
    end

    if !isempty(all_scores)
        println("📈 Score moyen: $(round(mean(all_scores), digits=6))")
        println("📊 Score médian: $(round(median(all_scores), digits=6))")
        println("📉 Score min/max: $(round(minimum(all_scores), digits=6)) / $(round(maximum(all_scores), digits=6))")
    end

    # Distribution des meilleures prédictions
    prediction_counts = Dict{String, Int}()
    for pred in best_predictions
        prediction_counts[pred] = get(prediction_counts, pred, 0) + 1
    end

    println("\n🏆 DISTRIBUTION DES MEILLEURES PRÉDICTIONS:")
    sorted_predictions = sort(collect(prediction_counts), by = x -> x[2], rev = true)
    for (index5, count) in sorted_predictions[1:min(5, length(sorted_predictions))]
        percentage = round(count / length(best_predictions) * 100, digits=2)
        println("   $index5: $count fois ($percentage%)")
    end

    println("="^80)
end

"""
    generate_detailed_simulation_report(
        results::Vector{HandSimulationResults{T}},
        filename::String = "simulation_report.txt"
    ) where T

Génère un rapport détaillé de simulation au format texte.
"""
function generate_detailed_simulation_report(
    results::Vector{HandSimulationResults{T}},
    filename::String = "simulation_report.txt"
) where T<:AbstractFloat

    if isempty(results)
        @warn "Aucun résultat à exporter"
        return
    end

    try
        open(filename, "w") do file
            # En-tête du rapport
            println(file, "RAPPORT DÉTAILLÉ DE SIMULATION PRÉDICTIVE INDEX5")
            println(file, "="^60)
            println(file, "Date: $(Dates.now())")
            println(file, "Nombre de mains simulées: $(length(results))")
            println(file, "")

            # Tableau détaillé pour chaque main
            println(file, "DÉTAIL PAR MAIN:")
            println(file, "-"^60)

            for (i, result) in enumerate(results)
                println(file, "")
                println(file, "MAIN $(result.hand_position) - INDEX5 observé: $(result.current_index5)")
                println(file, "INDEX1 obligatoire pour main $(result.hand_position + 1): $(result.required_index1)")
                println(file, "Meilleure prédiction: $(result.best_index5) (Score: $(round(result.best_score, digits=6)))")
                println(file, "")

                # Tableau des 9 simulations
                println(file, @sprintf("%-15s %10s %10s %10s %10s %10s %10s %10s %12s",
                        "INDEX5", "DiffEG", "DiffSEG", "DiffCEG", "Mt5", "DiffT5", "DiffDivEG", "DiffC", "SCORE"))
                println(file, "-"^115)

                # Trier par score décroissant
                sorted_sims = sort(collect(result.simulations), by = x -> x[2].score, rev = true)

                for (index5, sim) in sorted_sims
                    println(file, @sprintf("%-15s %10.4f %10.4f %10.4f %10.4f %10.4f %10.4f %10.4f %12.6f",
                            index5,
                            sim.diff_eg,
                            sim.diff_seg,
                            sim.diff_ceg,
                            sim.mt5,
                            sim.diff_t5,
                            sim.diff_div_eg,
                            sim.diff_c,
                            sim.score))
                end

                if i < length(results)
                    println(file, "")
                    println(file, "-"^60)
                end
            end
        end

        println("📄 Rapport détaillé généré: $filename")

    catch e
        @error "Erreur lors de la génération du rapport: $e"
    end
end

"""
    export_simulation_csv(
        results::Vector{HandSimulationResults{T}},
        filename::String = "simulation_data.csv"
    ) where T

Exporte les résultats de simulation au format CSV pour analyse externe.
"""
function export_simulation_csv(
    results::Vector{HandSimulationResults{T}},
    filename::String = "simulation_data.csv"
) where T<:AbstractFloat

    if isempty(results)
        @warn "Aucun résultat à exporter"
        return
    end

    try
        open(filename, "w") do file
            # En-tête CSV
            println(file, "Hand_Position,Current_INDEX5,Required_INDEX1,Simulated_INDEX5,Mt5,Conditionnelle,T5,DivEntropG,EntropG,EGobs,ConfEG,StructEG,DiffEG,DiffSEG,DiffCEG,DiffT5,DiffDivEG,DiffC,DiffEGobs,Score,Is_Best")

            # Données pour chaque simulation
            for result in results
                for (index5, sim) in result.simulations
                    is_best = (index5 == result.best_index5) ? "TRUE" : "FALSE"

                    println(file, "$(result.hand_position),$(result.current_index5),$(result.required_index1),$index5,$(sim.mt5),$(sim.conditionnelle),$(sim.t5),$(sim.div_entrop_g),$(sim.entrop_g),$(sim.eg_obs),$(sim.conf_eg),$(sim.struct_eg),$(sim.diff_eg),$(sim.diff_seg),$(sim.diff_ceg),$(sim.diff_t5),$(sim.diff_div_eg),$(sim.diff_c),$(sim.diff_egobs),$(sim.score),$is_best")
                end
            end
        end

        println("📊 Données CSV exportées: $filename")

    catch e
        @error "Erreur lors de l'export CSV: $e"
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# PROGRAMME PRINCIPAL AUTOMATISÉ
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main_simulation()

Programme principal automatisé pour la simulation prédictive INDEX5.
Charge automatiquement le fichier le plus récent et simule les mains 5-59.
"""
function main_simulation()
    println("🎰 SIMULATEUR PRÉDICTIF INDEX5 - BACCARAT")
    println("="^60)
    println("📋 Simulation automatisée des mains 5 à 59")
    println("📊 Calcul des 7 métriques pour 9 valeurs INDEX5 par main")
    println("🔧 Basé sur entropie_baccarat_analyzer.jl")
    println()

    try
        # Initialisation de l'analyseur
        println("⚙️  Initialisation de l'analyseur d'entropie...")
        analyzer = EntropyAnalyzer{Float64}()

        # Chargement automatique du fichier le plus récent
        println("🔍 Recherche du fichier le plus récent...")
        latest_file = find_latest_file_in_partie()

        # Chargement des données
        println("📁 Chargement des données...")
        parties = load_baccarat_data(latest_file)

        if isempty(parties)
            error("Aucune partie trouvée dans le fichier!")
        end

        println("✅ $(length(parties)) parties chargées")

        # Utiliser la première partie (ou demander à l'utilisateur)
        game_data = parties[1]
        sequence = extract_index5_sequence(game_data)

        if length(sequence) < 60
            error("Séquence trop courte! $(length(sequence)) mains trouvées, minimum 60 requises")
        end

        println("📏 Séquence: $(length(sequence)) mains")
        println("🎯 Simulation des mains 5 à 59 (prédiction mains 6 à 60)")
        println()

        # Lancer la simulation spécialisée
        results = simulate_hands_5_to_59(analyzer, sequence)

        # Afficher les résultats
        display_simulation_summary(results)

        # Génération automatique des rapports
        println("\n📄 Génération des rapports...")

        # Rapport détaillé
        report_filename = "simulation_mains_5_59_detailed.txt"
        generate_detailed_simulation_report(results, report_filename)

        # Export CSV
        csv_filename = "simulation_mains_5_59_data.csv"
        export_simulation_csv(results, csv_filename)

        println("\n✅ Simulation terminée avec succès!")
        println("📊 Consultez les fichiers générés pour les résultats détaillés")

    catch e
        println("❌ ERREUR: $e")
        println("🔧 Vérifiez que le dossier 'partie' contient des fichiers JSON valides")
    end
end

# Fonctions interactives supprimées - Programme automatisé uniquement

# ═══════════════════════════════════════════════════════════════════════════════
# POINT D'ENTRÉE AUTOMATISÉ
# ═══════════════════════════════════════════════════════════════════════════════

# Exécuter automatiquement la simulation au lancement
if abspath(PROGRAM_FILE) == @__FILE__
    main_simulation()
end
