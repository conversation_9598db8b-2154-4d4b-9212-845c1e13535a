"""
INDEX5 PREDICTIVE SIMULATOR - Simulateur de Prédiction INDEX5
=============================================================

Programme de simulation prédictive pour le baccarat basé sur INDEX5.
Calcule toutes les métriques d'entropie pour les 9 valeurs INDEX5 possibles
à la main n+1 selon les règles déterministes INDEX1.

Utilise entropie_baccarat_analyzer.jl comme moteur de calcul.

Architecture : Simulation exhaustive + Règles INDEX1 + Métriques complètes
"""

# Import du module principal d'analyse d'entropie
include("entropie_baccarat_analyzer.jl")

using Printf
using Statistics

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURES DE DONNÉES SPÉCIALISÉES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    PredictiveSimulationResult{T<:AbstractFloat}

Structure pour stocker les résultats de simulation pour une valeur INDEX5.
Contient toutes les métriques de base et différentiels calculés.
"""
struct PredictiveSimulationResult{T<:AbstractFloat}
    index5_value::String                    # Valeur INDEX5 simulée
    
    # Métriques de base (8 métriques)
    mt5::T                                  # Mt5 (Entropie Métrique)
    conditionnelle::T                       # Entropie Conditionnelle
    t5::T                                   # T5 (Taux d'Entropie)
    div_entrop_g::T                         # DivEntropG (Diversité Entropique)
    entrop_g::T                             # EntropG (Entropie Générale)
    eg_obs::T                               # EGobs (Entropie Générale Observée)
    conf_eg::T                              # ConfEG (Conformité Entropique)
    struct_eg::T                            # StructEG (Structure Entropique)
    
    # Différentiels (7 différentiels)
    diff_eg::T                              # DiffEG
    diff_seg::T                             # DiffSEG
    diff_ceg::T                             # DiffCEG
    diff_t5::T                              # DiffT5
    diff_div_eg::T                          # DiffDivEG
    diff_c::T                               # DiffC
    diff_egobs::T                           # DiffEGobs (bonus)
    
    # Score composite
    score::T                                # SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
end

"""
    HandSimulationResults{T<:AbstractFloat}

Structure pour stocker tous les résultats de simulation pour une main n.
Contient les 9 simulations possibles pour la main n+1.
"""
struct HandSimulationResults{T<:AbstractFloat}
    hand_position::Int                      # Position de la main n
    current_index5::String                  # INDEX5 observé à la main n
    required_index1::Int                    # INDEX1 obligatoire pour main n+1
    
    # Résultats pour les 9 valeurs possibles
    simulations::Dict{String, PredictiveSimulationResult{T}}
    
    # Statistiques de simulation
    best_index5::String                     # Meilleure prédiction (score max)
    best_score::T                           # Score maximal
    worst_index5::String                    # Pire prédiction (score min)
    worst_score::T                          # Score minimal
end

# ═══════════════════════════════════════════════════════════════════════════════
# RÈGLES INDEX1 DÉTERMINISTES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    apply_index1_rules(current_index5::String) -> Int

Applique les règles déterministes INDEX1 pour déterminer INDEX1 à la main n+1.

Règles :
- Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
- Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
"""
function apply_index1_rules(current_index5::String)
    if isempty(current_index5)
        return nothing
    end
    
    try
        parts = split(current_index5, '_')
        if length(parts) < 2
            return nothing
        end
        
        current_index1 = parse(Int, parts[1])
        current_index2 = parts[2]
        
        if current_index2 == "C"
            return 1 - current_index1  # Inversion obligatoire
        else  # A ou B
            return current_index1      # Conservation obligatoire
        end
    catch
        return nothing
    end
end

"""
    generate_possible_index5_values(required_index1::Int) -> Vector{String}

Génère les 9 valeurs INDEX5 possibles pour INDEX1 donné.
Format : INDEX1_INDEX2_INDEX3 avec INDEX2 ∈ {A,B,C} et INDEX3 ∈ {BANKER,PLAYER,TIE}
"""
function generate_possible_index5_values(required_index1::Int)
    possible_values = String[]
    
    for index2 in ["A", "B", "C"]
        for index3 in ["BANKER", "PLAYER", "TIE"]
            push!(possible_values, "$(required_index1)_$(index2)_$(index3)")
        end
    end
    
    return possible_values
end

# ═══════════════════════════════════════════════════════════════════════════════
# MOTEUR DE SIMULATION PRÉDICTIVE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    simulate_index5_prediction(
        analyzer::EntropyAnalyzer{T},
        sequence::Vector{String},
        hand_position::Int
    ) where T -> HandSimulationResults{T}

Simule toutes les valeurs INDEX5 possibles pour la main n+1 et calcule
toutes les métriques d'entropie et différentiels pour chaque simulation.
"""
function simulate_index5_prediction(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    hand_position::Int
) where T<:AbstractFloat
    
    if hand_position < 1 || hand_position > length(sequence)
        error("Position de main invalide: $hand_position")
    end
    
    # INDEX5 actuel à la main n
    current_index5 = sequence[hand_position]
    
    # Déterminer INDEX1 obligatoire pour main n+1
    required_index1 = apply_index1_rules(current_index5)
    if isnothing(required_index1)
        error("Impossible de déterminer INDEX1 pour: $current_index5")
    end
    
    # Générer les 9 valeurs possibles
    possible_values = generate_possible_index5_values(required_index1)
    
    # Calculer les métriques actuelles (main n)
    current_sequence = sequence[1:hand_position]
    current_evolution = calculate_all_metrics_evolution(analyzer, current_sequence, 4)
    
    if isempty(current_evolution)
        error("Impossible de calculer l'évolution pour la séquence actuelle")
    end
    
    current_metrics = current_evolution[end]
    
    # Dictionnaire pour stocker les résultats de simulation
    simulations = Dict{String, PredictiveSimulationResult{T}}()
    
    # Simuler chaque valeur INDEX5 possible
    for possible_index5 in possible_values
        # Créer la séquence simulée (main n + main n+1 simulée)
        simulated_sequence = vcat(current_sequence, [possible_index5])
        
        # Calculer les métriques pour la séquence simulée
        simulated_evolution = calculate_all_metrics_evolution(analyzer, simulated_sequence, 4)
        
        if isempty(simulated_evolution)
            continue  # Ignorer cette simulation si échec
        end
        
        simulated_metrics = simulated_evolution[end]
        
        # Calculer les différentiels entre main n et main n+1 simulée
        diff_eg = calculate_diff_entrop_g(simulated_metrics.simple_entropy_theoretical, current_metrics.simple_entropy_theoretical)
        diff_seg = calculate_diff_seg(simulated_metrics.struct_eg, current_metrics.struct_eg)
        diff_ceg = calculate_diff_ceg(simulated_metrics.conf_eg, current_metrics.conf_eg)
        diff_t5 = calculate_diff_taux(simulated_metrics.entropy_rate, current_metrics.entropy_rate)
        diff_div_eg = calculate_diff_div_entrop_g(simulated_metrics.simple_entropy, current_metrics.simple_entropy)
        diff_c = calculate_diff_cond(simulated_metrics.conditional_entropy, current_metrics.conditional_entropy)
        diff_egobs = calculate_diff_egobs(simulated_metrics.entropy_aep_observed, current_metrics.entropy_aep_observed)
        
        # Calculer le score composite
        score = calculate_predictive_score(diff_c, diff_t5, diff_div_eg, diff_eg)
        
        # Créer le résultat de simulation
        simulation_result = PredictiveSimulationResult{T}(
            possible_index5,
            simulated_metrics.metric_entropy,           # mt5
            simulated_metrics.conditional_entropy,      # conditionnelle
            simulated_metrics.entropy_rate,             # t5
            simulated_metrics.simple_entropy,           # div_entrop_g
            simulated_metrics.simple_entropy_theoretical, # entrop_g
            simulated_metrics.entropy_aep_observed,     # eg_obs
            simulated_metrics.conf_eg,                  # conf_eg
            simulated_metrics.struct_eg,                # struct_eg
            diff_eg,                                    # diff_eg
            diff_seg,                                   # diff_seg
            diff_ceg,                                   # diff_ceg
            diff_t5,                                    # diff_t5
            diff_div_eg,                                # diff_div_eg
            diff_c,                                     # diff_c
            diff_egobs,                                 # diff_egobs
            score                                       # score
        )
        
        simulations[possible_index5] = simulation_result
    end
    
    # Trouver la meilleure et la pire prédiction
    best_index5 = ""
    best_score = T(-Inf)
    worst_index5 = ""
    worst_score = T(Inf)
    
    for (index5, result) in simulations
        if result.score > best_score
            best_score = result.score
            best_index5 = index5
        end
        if result.score < worst_score
            worst_score = result.score
            worst_index5 = index5
        end
    end
    
    return HandSimulationResults{T}(
        hand_position,
        current_index5,
        required_index1,
        simulations,
        best_index5,
        best_score,
        worst_index5,
        worst_score
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# SIMULATION COMPLÈTE D'UNE SÉQUENCE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    simulate_complete_sequence(
        analyzer::EntropyAnalyzer{T},
        sequence::Vector{String}
    ) where T -> Vector{HandSimulationResults{T}}

Simule toutes les prédictions possibles pour chaque main de la séquence.
Retourne un vecteur de résultats de simulation pour chaque position.
"""
function simulate_complete_sequence(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat
    
    if length(sequence) < 2
        error("Séquence trop courte pour simulation (minimum 2 mains)")
    end
    
    results = HandSimulationResults{T}[]
    
    # Simuler pour chaque main (sauf la dernière car pas de main n+1)
    for hand_position in 1:(length(sequence)-1)
        try
            simulation_result = simulate_index5_prediction(analyzer, sequence, hand_position)
            push!(results, simulation_result)
            
            # Affichage de progression
            if hand_position % 10 == 0
                println("   Simulé: $hand_position/$(length(sequence)-1) mains")
            end
        catch e
            @warn "Erreur simulation main $hand_position: $e"
            continue
        end
    end
    
    return results
end

# ═══════════════════════════════════════════════════════════════════════════════
# AFFICHAGE ET RAPPORTS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    display_simulation_summary(results::Vector{HandSimulationResults{T}}) where T

Affiche un résumé des résultats de simulation.
"""
function display_simulation_summary(results::Vector{HandSimulationResults{T}}) where T<:AbstractFloat
    if isempty(results)
        println("❌ Aucun résultat de simulation à afficher")
        return
    end

    println("\n" * "="^80)
    println("📊 RÉSUMÉ DE SIMULATION PRÉDICTIVE INDEX5")
    println("="^80)
    println("📏 Nombre de mains simulées: $(length(results))")

    # Statistiques globales
    all_scores = Float64[]
    best_predictions = String[]

    for result in results
        for (index5, sim) in result.simulations
            push!(all_scores, sim.score)
        end
        push!(best_predictions, result.best_index5)
    end

    if !isempty(all_scores)
        println("📈 Score moyen: $(round(mean(all_scores), digits=6))")
        println("📊 Score médian: $(round(median(all_scores), digits=6))")
        println("📉 Score min/max: $(round(minimum(all_scores), digits=6)) / $(round(maximum(all_scores), digits=6))")
    end

    # Distribution des meilleures prédictions
    prediction_counts = Dict{String, Int}()
    for pred in best_predictions
        prediction_counts[pred] = get(prediction_counts, pred, 0) + 1
    end

    println("\n🏆 DISTRIBUTION DES MEILLEURES PRÉDICTIONS:")
    sorted_predictions = sort(collect(prediction_counts), by = x -> x[2], rev = true)
    for (index5, count) in sorted_predictions[1:min(5, length(sorted_predictions))]
        percentage = round(count / length(best_predictions) * 100, digits=2)
        println("   $index5: $count fois ($percentage%)")
    end

    println("="^80)
end

"""
    generate_detailed_simulation_report(
        results::Vector{HandSimulationResults{T}},
        filename::String = "simulation_report.txt"
    ) where T

Génère un rapport détaillé de simulation au format texte.
"""
function generate_detailed_simulation_report(
    results::Vector{HandSimulationResults{T}},
    filename::String = "simulation_report.txt"
) where T<:AbstractFloat

    if isempty(results)
        @warn "Aucun résultat à exporter"
        return
    end

    try
        open(filename, "w") do file
            # En-tête du rapport
            println(file, "RAPPORT DÉTAILLÉ DE SIMULATION PRÉDICTIVE INDEX5")
            println(file, "="^60)
            println(file, "Date: $(now())")
            println(file, "Nombre de mains simulées: $(length(results))")
            println(file, "")

            # Tableau détaillé pour chaque main
            println(file, "DÉTAIL PAR MAIN:")
            println(file, "-"^60)

            for (i, result) in enumerate(results)
                println(file, "")
                println(file, "MAIN $(result.hand_position) - INDEX5 observé: $(result.current_index5)")
                println(file, "INDEX1 obligatoire pour main $(result.hand_position + 1): $(result.required_index1)")
                println(file, "Meilleure prédiction: $(result.best_index5) (Score: $(round(result.best_score, digits=6)))")
                println(file, "")

                # Tableau des 9 simulations
                println(file, @sprintf("%-15s %10s %10s %10s %10s %10s %10s %10s %12s",
                        "INDEX5", "DiffEG", "DiffSEG", "DiffCEG", "Mt5", "DiffT5", "DiffDivEG", "DiffC", "SCORE"))
                println(file, "-"^115)

                # Trier par score décroissant
                sorted_sims = sort(collect(result.simulations), by = x -> x[2].score, rev = true)

                for (index5, sim) in sorted_sims
                    println(file, @sprintf("%-15s %10.4f %10.4f %10.4f %10.4f %10.4f %10.4f %10.4f %12.6f",
                            index5,
                            sim.diff_eg,
                            sim.diff_seg,
                            sim.diff_ceg,
                            sim.mt5,
                            sim.diff_t5,
                            sim.diff_div_eg,
                            sim.diff_c,
                            sim.score))
                end

                if i < length(results)
                    println(file, "")
                    println(file, "-"^60)
                end
            end
        end

        println("📄 Rapport détaillé généré: $filename")

    catch e
        @error "Erreur lors de la génération du rapport: $e"
    end
end

"""
    export_simulation_csv(
        results::Vector{HandSimulationResults{T}},
        filename::String = "simulation_data.csv"
    ) where T

Exporte les résultats de simulation au format CSV pour analyse externe.
"""
function export_simulation_csv(
    results::Vector{HandSimulationResults{T}},
    filename::String = "simulation_data.csv"
) where T<:AbstractFloat

    if isempty(results)
        @warn "Aucun résultat à exporter"
        return
    end

    try
        open(filename, "w") do file
            # En-tête CSV
            println(file, "Hand_Position,Current_INDEX5,Required_INDEX1,Simulated_INDEX5,Mt5,Conditionnelle,T5,DivEntropG,EntropG,EGobs,ConfEG,StructEG,DiffEG,DiffSEG,DiffCEG,DiffT5,DiffDivEG,DiffC,DiffEGobs,Score,Is_Best")

            # Données pour chaque simulation
            for result in results
                for (index5, sim) in result.simulations
                    is_best = (index5 == result.best_index5) ? "TRUE" : "FALSE"

                    println(file, "$(result.hand_position),$(result.current_index5),$(result.required_index1),$index5,$(sim.mt5),$(sim.conditionnelle),$(sim.t5),$(sim.div_entrop_g),$(sim.entrop_g),$(sim.eg_obs),$(sim.conf_eg),$(sim.struct_eg),$(sim.diff_eg),$(sim.diff_seg),$(sim.diff_ceg),$(sim.diff_t5),$(sim.diff_div_eg),$(sim.diff_c),$(sim.diff_egobs),$(sim.score),$is_best")
                end
            end
        end

        println("📊 Données CSV exportées: $filename")

    catch e
        @error "Erreur lors de l'export CSV: $e"
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# INTERFACE PRINCIPALE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main_simulation()

Point d'entrée principal pour la simulation prédictive INDEX5.
"""
function main_simulation()
    println("🎰 SIMULATEUR PRÉDICTIF INDEX5 - BACCARAT")
    println("=" * "="^50)
    println("Simulation exhaustive des 9 valeurs INDEX5 possibles")
    println("Basé sur entropie_baccarat_analyzer.jl")
    println()

    # Initialisation de l'analyseur
    analyzer = EntropyAnalyzer{Float64}()

    # Chemin par défaut du fichier JSON
    default_filepath = "partie/dataset_baccarat_lupasco_20250704_092825_condensed.json"

    while true
        println("\n🎯 MENU SIMULATION:")
        println("1. Simuler une partie unique")
        println("2. Simuler plusieurs parties")
        println("3. Simuler une séquence personnalisée")
        println("4. Quitter")

        print("\nVotre choix (1-4): ")
        choix = readline()

        if choix == "1"
            simulate_single_game_interactive(analyzer, default_filepath)
        elseif choix == "2"
            simulate_multiple_games_interactive(analyzer, default_filepath)
        elseif choix == "3"
            simulate_custom_sequence_interactive(analyzer)
        elseif choix == "4"
            println("👋 Au revoir! Merci d'avoir utilisé le simulateur prédictif!")
            break
        else
            println("❌ Choix invalide!")
        end
    end
end

"""
    simulate_single_game_interactive(analyzer::EntropyAnalyzer, filepath::String)

Interface interactive pour simuler une partie unique.
"""
function simulate_single_game_interactive(analyzer::EntropyAnalyzer, filepath::String)
    println("\n📁 Chargement des données...")
    parties = load_baccarat_data(filepath)

    if isempty(parties)
        return
    end

    println("📊 $(length(parties)) parties disponibles")
    print("Numéro de la partie à simuler (1-$(length(parties))): ")

    try
        partie_num = parse(Int, readline())
        if partie_num < 1 || partie_num > length(parties)
            println("❌ Numéro de partie invalide!")
            return
        end

        game_data = parties[partie_num]
        sequence = extract_index5_sequence(game_data)

        if length(sequence) < 2
            println("❌ Séquence trop courte pour simulation!")
            return
        end

        println("\n🔄 Simulation en cours...")
        println("📏 Séquence: $(length(sequence)) mains")

        # Lancer la simulation
        results = simulate_complete_sequence(analyzer, sequence)

        # Afficher les résultats
        display_simulation_summary(results)

        # Proposer l'export
        print("\n💾 Générer un rapport détaillé? (o/n): ")
        if lowercase(strip(readline())) == "o"
            report_filename = "simulation_partie$(partie_num).txt"
            generate_detailed_simulation_report(results, report_filename)
        end

        print("💾 Exporter en CSV? (o/n): ")
        if lowercase(strip(readline())) == "o"
            csv_filename = "simulation_partie$(partie_num).csv"
            export_simulation_csv(results, csv_filename)
        end

    catch e
        println("❌ Erreur: $e")
    end
end

"""
    simulate_multiple_games_interactive(analyzer::EntropyAnalyzer, filepath::String)

Interface interactive pour simuler plusieurs parties.
"""
function simulate_multiple_games_interactive(analyzer::EntropyAnalyzer, filepath::String)
    println("\n📁 Chargement des données...")
    parties = load_baccarat_data(filepath)

    if isempty(parties)
        return
    end

    println("📊 $(length(parties)) parties disponibles")
    print("Nombre de parties à simuler (max $(length(parties))): ")

    try
        num_parties = parse(Int, readline())
        if num_parties < 1 || num_parties > length(parties)
            println("❌ Nombre invalide!")
            return
        end

        println("\n🔄 Simulation de $num_parties parties en cours...")

        all_results = HandSimulationResults{Float64}[]
        successful_simulations = 0

        for i in 1:num_parties
            try
                game_data = parties[i]
                sequence = extract_index5_sequence(game_data)

                if length(sequence) >= 2
                    results = simulate_complete_sequence(analyzer, sequence)
                    append!(all_results, results)
                    successful_simulations += 1
                end

                if i % 5 == 0
                    println("   Parties simulées: $i/$num_parties")
                end
            catch e
                @warn "Erreur partie $i: $e"
                continue
            end
        end

        # Afficher les résultats globaux
        println("\n" * "="^80)
        println("📈 RÉSULTATS GLOBAUX ($successful_simulations parties simulées)")
        println("="^80)

        display_simulation_summary(all_results)

        # Proposer l'export global
        print("\n💾 Générer un rapport global? (o/n): ")
        if lowercase(strip(readline())) == "o"
            generate_detailed_simulation_report(all_results, "simulation_globale.txt")
        end

        print("💾 Exporter en CSV global? (o/n): ")
        if lowercase(strip(readline())) == "o"
            export_simulation_csv(all_results, "simulation_globale.csv")
        end

    catch e
        println("❌ Erreur: $e")
    end
end

"""
    simulate_custom_sequence_interactive(analyzer::EntropyAnalyzer)

Interface interactive pour simuler une séquence personnalisée.
"""
function simulate_custom_sequence_interactive(analyzer::EntropyAnalyzer)
    println("\n🔮 SIMULATION SÉQUENCE PERSONNALISÉE")
    println("="^50)
    println("Entrez une séquence INDEX5 (format: 0_A_BANKER,1_C_TIE,...)")
    println("Minimum 2 valeurs. Tapez 'quit' pour revenir au menu.")

    while true
        print("\nSéquence INDEX5: ")
        input = strip(readline())

        if lowercase(input) == "quit"
            break
        end

        if isempty(input)
            println("❌ Séquence vide!")
            continue
        end

        # Parser la séquence
        try
            sequence = String.(strip.(split(input, ',')))

            # Valider le format INDEX5
            valid_sequence = true
            for value in sequence
                if !occursin(r"^[01]_[ABC]_(BANKER|PLAYER|TIE)$", value)
                    println("❌ Format invalide pour '$value'. Format attendu: INDEX1_INDEX2_INDEX3")
                    valid_sequence = false
                    break
                end
            end

            if !valid_sequence
                continue
            end

            if length(sequence) < 2
                println("❌ Minimum 2 valeurs requises!")
                continue
            end

            println("\n🔄 Simulation en cours...")
            println("📏 Séquence: $(length(sequence)) mains")

            # Lancer la simulation
            results = simulate_complete_sequence(analyzer, sequence)

            # Afficher les résultats
            display_simulation_summary(results)

            # Proposer l'export
            print("\n💾 Générer un rapport? (o/n): ")
            if lowercase(strip(readline())) == "o"
                generate_detailed_simulation_report(results, "simulation_personnalisee.txt")
            end

        catch e
            println("❌ Erreur lors de la simulation: $e")
        end
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# POINT D'ENTRÉE
# ═══════════════════════════════════════════════════════════════════════════════

# Exécuter le programme principal si ce fichier est exécuté directement
if abspath(PROGRAM_FILE) == @__FILE__
    main_simulation()
end
